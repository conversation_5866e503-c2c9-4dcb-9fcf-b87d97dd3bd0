{"cells": [{"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "8eca3106-b59f-47d7-ba3c-f6294a50f0a6", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["\n", "merge into gold.gld_dim_entity target\n", "\tusing (\n", "\t\t\t   select integration_id\n", "\t\t\t\t      , entity_id\n", "\t\t\t        , entity_code\n", "\t\t\t\t      , entity_name_en\n", "\t\t\t\t      , entity_name_ar\n", "\t\t\t\t\t\t\t, entity_type\n", "\t\t\t\t\t\t\t, sector\n", "\t\t\t\t      , entity_parent_code\n", "\t\t\t\t      , parent_flag\n", "\t\t\t\t      , dep_adeo_resolution_flag\n", "\t\t\t\t      , oracle_hrms_flag\n", "\t\t\t\t      , follow_hr_law_flag\n", "\t\t\t\t\t\t\t, follow_hr_scale_flag\n", "\t\t\t\t      , status\n", "           from silver.slvr_entity\n", "\t\t    ) source\n", "       on source.integration_id = target.integration_id\n", "\t\t\tand target.current_flag   = true\n", "\n", "\twhen matched \n", "\t\t\t\t\t and (\n", "\t\t\t\t\t\t        target.entity_name_ar           <> source.entity_name_ar\n", "\t\t\t\t\t\t\t\t or target.entity_type              <> source.entity_type\t\t\n", "\t\t\t\t\t\t     or target.sector                   <> source.sector\n", "\t\t\t\t\t\t     or target.entity_parent_code       <> source.entity_parent_code\n", "\t\t\t\t\t\t\t\t or target.parent_flag              <> source.parent_flag\n", "\t\t\t\t\t\t\t\t or target.dep_adeo_resolution_flag <> source.dep_adeo_resolution_flag\n", "\t\t\t\t\t\t     or target.oracle_hrms_flag         <> source.oracle_hrms_flag\n", "\t\t\t\t\t\t\t\t or target.follow_hr_law_flag       <> source.follow_hr_law_flag\n", "\t\t\t\t\t\t\t\t or target.follow_hr_scale_flag     <> source.follow_hr_scale_flag\n", "\t\t\t\t\t\t     or target.status                   <> source.status\t\t\t\t\t\t\t\t \t\t\t\t\t\t     \n", "\t\t\t\t\t     )\n", "\t\tthen update \n", "\t\t        set target.effective_to             = current_date()\n", "\t\t\t\t      , target.modified_at_ts           = current_timestamp()\n", "\t\t\t\t\t\t\t, target.current_flag             = false\n", "\n", "\twhen not matched then\n", "\t\tinsert (\n", "\t\t\t\t      integration_id\n", "\t\t\t\t    , entity_id\n", "\t\t\t\t    , entity_code\n", "\t\t\t\t    , entity_name_en\n", "\t\t\t\t\t\t, entity_name_ar\n", "\t\t\t\t    , entity_type\n", "\t\t\t\t    , sector\n", "\t\t\t\t    , entity_parent_code\n", "\t\t\t\t    , parent_flag\n", "\t\t\t\t    , dep_adeo_resolution_flag\n", "\t\t\t\t\t\t, oracle_hrms_flag\n", "\t\t\t\t\t\t, follow_hr_law_flag\n", "\t\t\t\t\t\t, follow_hr_scale_flag\n", "\t\t\t\t    , status\n", "            , effective_from\n", "            , effective_to\n", "\t\t\t\t    , created_at_ts\n", "  \t\t\t\t  , modified_at_ts\n", "            , current_flag\n", "\t\t       )\n", "\t\tvalues (\n", "\t\t\t\t      source.integration_id\n", "\t\t\t\t    , source.entity_id\n", "    \t\t\t\t, source.entity_code\n", "\t\t\t\t    , source.entity_name_en\n", "\t\t\t\t\t\t, source.entity_name_ar\n", "\t\t\t\t    , source.entity_type\n", "\t\t\t\t    , source.sector\n", "\t\t\t\t    , source.entity_parent_code\n", "\t\t\t\t    , source.parent_flag\n", "\t\t\t\t    , source.dep_adeo_resolution_flag\n", "\t\t\t\t\t\t, source.oracle_hrms_flag\n", "            , source.follow_hr_law_flag\n", "            , source.follow_hr_scale_flag\n", "\t\t\t\t    , source.status\n", "            , current_date()\n", "            , date_format('9999-12-31', 'yyyy-MM-dd')\n", "\t\t\t\t    , current_timestamp() \n", "\t\t\t\t    , current_timestamp() \n", "            , true\n", "\t\t       )\n", "; \n", "\n", "\n", "-- insert new version of the row\n", "merge into gold.gld_dim_entity target\n", "\tusing (\n", "\t\t\t   select integration_id\n", "\t\t\t\t      , entity_id\n", "\t\t\t        , entity_code\n", "\t\t\t\t      , entity_name_en\n", "\t\t\t\t      , entity_name_ar\n", "\t\t\t\t\t\t\t, entity_type\n", "\t\t\t\t\t\t\t, sector\n", "\t\t\t\t      , entity_parent_code\n", "\t\t\t\t      , parent_flag\n", "\t\t\t\t      , dep_adeo_resolution_flag\n", "\t\t\t\t      , oracle_hrms_flag\n", "\t\t\t\t      , follow_hr_law_flag\n", "\t\t\t\t\t\t\t, follow_hr_scale_flag\n", "\t\t\t\t      , status\n", "           from silver.slvr_entity\n", "\t\t    ) source\n", "       on source.integration_id = target.integration_id\n", "\t\t\tand target.current_flag   = true\n", "when not matched then\n", "\t\tinsert (\n", "\t\t\t\t      integration_id\n", "\t\t\t\t    , entity_id\n", "\t\t\t\t    , entity_code\n", "\t\t\t\t    , entity_name_en\n", "\t\t\t\t\t\t, entity_name_ar\n", "\t\t\t\t    , entity_type\n", "\t\t\t\t    , sector\n", "\t\t\t\t    , entity_parent_code\n", "\t\t\t\t    , parent_flag\n", "\t\t\t\t    , dep_adeo_resolution_flag\n", "\t\t\t\t\t\t, oracle_hrms_flag\n", "\t\t\t\t\t\t, follow_hr_law_flag\n", "\t\t\t\t\t\t, follow_hr_scale_flag\n", "\t\t\t\t    , status\n", "            , effective_from\n", "            , effective_to\n", "\t\t\t\t    , created_at_ts\n", "  \t\t\t\t  , modified_at_ts\n", "            , current_flag\n", "\t\t       )\n", "\t\tvalues (\n", "\t\t\t\t      source.integration_id\n", "\t\t\t\t    , source.entity_id\n", "    \t\t\t\t, source.entity_code\n", "\t\t\t\t    , source.entity_name_en\n", "\t\t\t\t\t\t, source.entity_name_ar\n", "\t\t\t\t    , source.entity_type\n", "\t\t\t\t    , source.sector\n", "\t\t\t\t    , source.entity_parent_code\n", "\t\t\t\t    , source.parent_flag\n", "\t\t\t\t    , source.dep_adeo_resolution_flag\n", "\t\t\t\t\t\t, source.oracle_hrms_flag\n", "            , source.follow_hr_law_flag\n", "            , source.follow_hr_scale_flag\n", "\t\t\t\t    , source.status\n", "            , current_date()\n", "            , date_format('9999-12-31', 'yyyy-MM-dd')\n", "\t\t\t\t    , current_timestamp() \n", "\t\t\t\t    , current_timestamp() \n", "            , true\n", "\t\t       )\n", "; \n", "\n", "\n", "\n", "/*\n", "  /* if the row is not in the Source table, then a soft delete is set*/\n", "\twhen not matched by source\n", "\t and target.current_flag = true then\n", "\t  update \n", "\t\t   set target.current_flag   = false  --soft delete\n", "\t\t     , target.modified_at_ts = current_timestamp()\n", "*/\n"]}], "metadata": {"application/vnd.databricks.v1+notebook": {"computePreferences": null, "dashboards": [], "environmentMetadata": {"base_environment": "", "environment_version": "2"}, "inputWidgetPreferences": null, "language": "sql", "notebookMetadata": {"pythonIndentUnit": 4}, "notebookName": "gld_dim_entity", "widgets": {}}, "language_info": {"name": "sql"}}, "nbformat": 4, "nbformat_minor": 0}