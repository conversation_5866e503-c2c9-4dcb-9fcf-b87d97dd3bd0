{"cells": [{"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "8eae4b43-b80e-460b-b7ff-9b47281f850e", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["from pyspark.sql import SparkSession\n", "from pyspark.sql.functions import (\n", "    col, lit, date_format, expr, sequence, to_date, year, quarter, month, dayofmonth,\n", "    weekofyear, dayofweek, dayofyear, format_string, concat_ws, when, lpad\n", ")\n", "from pyspark.sql.types import IntegerType\n", "\n", "def generate_date_table(start_year=1990, end_year=2090):\n", "    # Creates start and end dates\n", "    start_date = f\"{start_year}-01-01\"\n", "    end_date = f\"{end_year}-12-31\"\n", "    \n", "    # Generate date sequence\n", "    date_df = spark.sql(f\"SELECT sequence(to_date('{start_date}'), to_date('{end_date}'), interval 1 day) as date_seq\") \\\n", "                  .withColumn(\"date\", expr(\"explode(date_seq)\")) \\\n", "                  .drop(\"date_seq\")\n", "\n", "    # Calculate semesters: 1 for months 1-6, 2 for monts 7-12\n", "    date_df = date_df.withColumn(\"month\", month(col(\"date\")))\n", "    date_df = date_df.withColumn(\"semester\", when(col(\"month\") <= 6, 1).otherwise(2))\n", "    \n", "    # Add necessary columns \n", "    date_df = date_df \\\n", "        .withColumn(\"datekey\", date_format(col(\"date\"), \"yyyyMMdd\").cast(IntegerType())) \\\n", "        .withColumn(\"year\", year(col(\"date\"))) \\\n", "        .withColumn(\"quarter\", quarter(col(\"date\"))) \\\n", "        .withColumn(\"day\", dayofmonth(col(\"date\"))) \\\n", "        .withColumn(\"week\", weekofyear(col(\"date\"))) \\\n", "        .withColumn(\"semesternamelong\", concat_ws(\" \", lit(\"Semester\"), col(\"semester\"))) \\\n", "        .withColumn(\"semesternameshort\", concat_ws(\"\", lit(\"S\"), col(\"semester\"))) \\\n", "        .withColumn(\"quarternamelong\", concat_ws(\" \", lit(\"Quarter\"), col(\"quarter\"))) \\\n", "        .withColumn(\"quarternameshort\", concat_ws(\"\", lit(\"Q\"), col(\"quarter\"))) \\\n", "        .withColumn(\"monthnamelong\", date_format(col(\"date\"), \"MMMM\")) \\\n", "        .withColumn(\"monthnameshort\", date_format(col(\"date\"), \"MMM\")) \\\n", "        .withColumn(\"weeknamelong\", concat_ws(\" \", lit(\"Week\"), col(\"week\"))) \\\n", "        .withColumn(\"weeknameshort\", concat_ws(\"\", lit(\"W\"), col(\"week\"))) \\\n", "        .withColumn(\"dayofweek\", when(dayofweek(col(\"date\")) == 1, 7).otherwise(dayofweek(col(\"date\")) - 1)) \\\n", "        .withColumn(\"yearmonthid\", date_format(col(\"date\"), \"yyyyMM\").cast(IntegerType())) \\\n", "        .withColumn(\"yearmonthstring\", date_format(col(\"date\"), \"yyyy-MMM\")) \\\n", "        .withColumn(\"yearsemesterstring\", concat_ws(\"-\", col(\"year\"), concat_ws(\"\", lit(\"S\"), col(\"semester\")))) \\\n", "        .withColumn(\"yearquarterstring\", concat_ws(\"-\", col(\"year\"), concat_ws(\"\", lit(\"Q\"), col(\"quarter\")))) \\\n", "        .withColumn(\"yearweekstring\", concat_ws(\"-\", col(\"year\"), concat_ws(\"\", lit(\"W\"), lpad(col(\"week\").cast(\"string\"), 2, \"0\")))) \\\n", "        .withColumn(\"monthyearstring\", date_format(col(\"date\"), \"MMyyyy\")) \\\n", "        .withColumn(\"monthlongyearstring\", date_format(col(\"date\"), \"MMMM yyyy\")) \\\n", "        .withColumn(\"dayofweeknamelong\", date_format(col(\"date\"), \"EEEE\")) \\\n", "        .withColumn(\"dayofweeknameshort\", date_format(col(\"date\"), \"E\")) \\\n", "        .withColumn(\"dayofyear\", dayofyear(col(\"date\")))\n", "\n", "    return date_df\n", "\n", "# Create table\n", "date_table_df = generate_date_table()\n", "\n", "# Show example\n", "date_table_df.show(10)\n", "\n", "# Save as table\n", "date_table_df.write.mode(\"overwrite\").saveAsTable(\"common.dim_date_calendar\")"]}], "metadata": {"application/vnd.databricks.v1+notebook": {"computePreferences": null, "dashboards": [], "environmentMetadata": {"base_environment": "", "environment_version": "2"}, "inputWidgetPreferences": null, "language": "python", "notebookMetadata": {"pythonIndentUnit": 4}, "notebookName": "dim_date_calendar", "widgets": {}}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}