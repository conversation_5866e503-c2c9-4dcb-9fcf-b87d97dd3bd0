{"cells": [{"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "b82cf55c-43d8-43e7-9fc3-5e8fd2d1015c", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["-------------------------------------------------------------------------------------------------------------------------\n", "-- table       : slvr_entity\n", "-- description : This is the list of all the entities\n", "-- created by  : <PERSON><PERSON>\n", "-------------------------------------------------------------------------------------------------------------------------\n", "\n", "DROP TABLE IF EXISTS silver.slvr_entity;\n", "\n", "CREATE TABLE IF NOT EXISTS silver.slvr_entity (\n", "  integration_id           string    comment 'ID based on concatenation of PK fields',\n", "  entity_id                int       comment 'Id of the entity',\n", "  entity_code              string    comment 'Code of the Entity',\n", "  entity_name_en           string    comment 'Name of the Entity, in english',\n", "  entity_name_ar           string    comment 'Name of the Entity, in arabic',\n", "  entity_type              string    comment 'TBD',\n", "  sector                   string    comment 'Sector to which the entity belongs',\n", "  entity_parent_code       string    comment 'Code of the Entitys Parent',\n", "  parent_flag              boolean   comment 'Flag to indicate whether the entity is Parent or Not',\n", "  dep_adeo_resolution_flag boolean   comment 'TBD',\n", "  oracle_hrms_flag         boolean   comment 'TBD',\n", "  follow_hr_law_flag       boolean   comment 'TBD',\n", "  follow_hr_scale_flag     boolean   comment 'TBD',\n", "  status                   string    comment 'Current status of the entity',\n", "  created_at_ts            timestamp comment 'Control field - Date & Time when the row was created',\n", "  modified_at_ts           timestamp comment 'Control field - Date & Time when the row was last modified'\n", ")USING DELTA\n", "\n", "COMMENT 'This is the list of all the entities';\n", "ALTER TABLE silver.slvr_entity SET TAGS ('workstream' = 'transversal');\n", "\n", "-------------------------------------------------------------------------------------------------------------------------\n", "-------------------------------------------------------------------------------------------------------------------------\n"]}], "metadata": {"application/vnd.databricks.v1+notebook": {"computePreferences": null, "dashboards": [], "environmentMetadata": {"base_environment": "", "environment_version": "2"}, "inputWidgetPreferences": null, "language": "sql", "notebookMetadata": {"pythonIndentUnit": 4}, "notebookName": "create_silver_entity", "widgets": {}}, "language_info": {"name": "sql"}}, "nbformat": 4, "nbformat_minor": 0}