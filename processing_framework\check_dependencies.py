# Databricks notebook source
# MAGIC %pip install -r ../requirements.txt

# COMMAND ----------

control_table = dbutils.widgets.get("control_table")
notebook_path = dbutils.widgets.get("notebook_path")

# COMMAND ----------

print(f'{control_table=}')
print(f'{notebook_path=}')

# COMMAND ----------

# %run ../common/metadata_sql
from gd_obsd_common.sql_framework import MetadataSQL

# COMMAND ----------

import pyspark.sql.functions as F

md = MetadataSQL(spark)
query = f"SELECT NotebookPath, Dependencies, Enabled FROM {control_table}"
print(query)
df = md.read(query)

# COMMAND ----------

# MAGIC %md
# MAGIC ## Check if notebook is enabled

# COMMAND ----------

is_enabled = df.filter(F.col("NotebookPath") == notebook_path).select("Enabled").collect()[0]["Enabled"]
if not is_enabled:
    # Skip notebook
    raise Exception(f"Notebook '{notebook_path}' is not enabled")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Get notebook dependencies

# COMMAND ----------

dependencies_df = (
    df.filter(F.col("NotebookPath") == notebook_path)
    .select(F.explode(F.split(F.col("Dependencies"), ",")).alias("dependency"))
    .select(F.trim(F.col("dependency")).alias("dependency"))
    .filter(F.col("dependency").isNotNull())
)
dependencies = [row["dependency"] for row in dependencies_df.collect()]
if len(dependencies) == 0:
    # Notebook can be executed if it has no dependencies
    dbutils.notebook.exit(f"Notebook '{notebook_path}' has no dependencies.")
else:
    print(f"Notebook '{notebook_path}' has {len(dependencies)} dependencies: {dependencies}")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Check if dependencies have been executed successfully

# COMMAND ----------

query = f"""
SELECT TOP 1 WITH TIES 
    TableName,
    Status,
    StartTime
FROM [Log].[ProcessingFrameworkChildLog]
WHERE TableName IN ({",".join(f"'{dep}'" for dep in dependencies)})
  AND StartTime >= DATEADD(HOUR, -24, GETDATE()) -- Filter last 24 hours
ORDER BY ROW_NUMBER() OVER (PARTITION BY TableName ORDER BY StartTime DESC)
"""
print(query)
dependency_logs = md.read(query).collect()
print(dependency_logs)

if len(dependencies) != len(dependency_logs):
    tables = [row["TableName"] for row in dependency_logs]
    raise Exception(f"Skipping notebook '{notebook_path}' because of missing dependencies. Dependency tables not found in the logs, which means they have not been run in the expected time frame. Dependencies found: {tables}. Expected: {dependencies}")

if sum(1 for row in dependency_logs if row['Status'] != 'Success') == 0:
    ok_message = f"All dependencies for notebook '{notebook_path}' are enabled and have been successfully processed"
    print(ok_message)
    dbutils.notebook.exit(ok_message)

error_message = f"Notebook '{notebook_path}' has skipped or failed dependencies:"
for dependency_log in dependency_logs:
    error_message += f"\n\t- '{dependency_log['TableName']}' has status '{dependency_log['Status']}'"

# Skip notebook if a dependency has failed or been skipped
raise Exception(error_message)
