# Databricks notebook source
stream = dbutils.widgets.get("stream")
group_id = dbutils.widgets.get("group_id")

# COMMAND ----------

!pip install gd_obsd_common

# COMMAND ----------

# %run ./teams_alert
from gd_obsd_common.teams_alert import <PERSON><PERSON><PERSON>t, AlertMessage, AlertType, format_duration, get_adf_url

# COMMAND ----------

# %run ../common/metadata_sql
from gd_obsd_common.sql_framework import MetadataSQL



# COMMAND ----------

# Load JSON config with all webhooks
import json
import os
with open('../alerts/teams_webhooks.json', 'r') as f:
    teams_channels_webhooks = json.load(f)

# COMMAND ----------

# Initialize Teams Alert
import os
env = os.environ.get('ENVIRONMENT', 'dev')
channel_webhook = teams_channels_webhooks[env].get(stream)
if not channel_webhook:
    channel_webhook = teams_channels_webhooks[env].get('all')
    if not channel_webhook:
        raise ValueError(f"Webhook URL for stream '{stream}' not found in the configuration.")
teams_alert = TeamsAlert(channel_webhook)

# COMMAND ----------

md = MetadataSQL(spark)
'''
df = md.read(
f"""
select 
    RunId,
    DatasetName,
    DatasetStatus,
    Status,
    case 
      when DatasetEndTime is null then -1 
      else DATEDIFF(second, DatasetStartTime, DatasetEndTime) 
    end as Duration
from [Log].[PowerBiRefreshLog] 
where
groupid = '{group_id}'
and stream = '{stream}'
"""
)
'''
timediff = md.read(
f"""
SELECT
    datediff(second, min(DatasetStartTime), max(DatasetEndTime)) as Duration
FROM [Log].[PowerBiRefreshLog]
WHERE
GroupId = '{group_id}'
and Stream = '{stream}'
"""
).first()

# COMMAND ----------

rows = md.read(
f"""
SELECT
    SUM(CASE WHEN Status = 'Success' THEN 1 ELSE 0 END) AS SuccessTriggered,
    SUM(CASE WHEN Status != 'Success' THEN 1 ELSE 0 END) AS NonSuccessTriggered,
    SUM(CASE WHEN DatasetStatus = 'Completed' THEN 1 ELSE 0 END) AS SuccessDataset,
    SUM(CASE WHEN DatasetStatus != 'Completed' THEN 1 ELSE 0 END) AS NonSuccessDataset
FROM [Log].[PowerBiRefreshLog]
WHERE
GroupId = '{group_id}'
and Stream = '{stream}'
"""
).first()

success_triggered = rows['SuccessTriggered'] or 0
non_success_triggered = rows['NonSuccessTriggered'] or 0
success_dataset = rows['SuccessDataset'] or 0
non_success_dataset = rows['NonSuccessDataset'] or 0

is_success = (non_success_triggered + non_success_dataset) == 0

message = f"The PowerBi Refresh for the stream `{stream}` has completed successfully." if is_success else f"The PowerBi Refresh for the stream `{stream}` contains failures."

alert = AlertMessage(
  f"PowerBi Refresh Framework for {stream.replace('_', ' ').title()}",
  message,
  AlertType.SUCCESS if is_success else AlertType.ERROR
)
# for row in df.collect():
#   metrics[row['DatasetName']] = "v" if row['Status'] == "Success" and row['DatasetStatus'] == "Completed" else "X"
metrics = {"Successful Refreshes Triggered": success_triggered, "Failed Refreshes Triggered": non_success_triggered, "Successful Refreshes": success_dataset, "Failed Refreshes": non_success_dataset, "Duration": format_duration(timediff.Duration or 0), "GroupId": group_id}

actions = [{"name": "View ADF", "url": get_adf_url(group_id)}]
success = teams_alert.send_alert(alert, metrics=metrics, actions=actions)
