# Databricks notebook source
# MAGIC %pip install -r ../requirements.txt
# MAGIC

# COMMAND ----------

control_table = dbutils.widgets.get("control_table")
max_concurrent_notebooks = int(dbutils.widgets.get("max_concurrent_notebooks"))
stream = dbutils.widgets.get("stream")

# COMMAND ----------

print(f'{control_table=}')
print(f'{max_concurrent_notebooks=}')
print(f'{stream=}')

# COMMAND ----------

from typing import Any


class DAG:
    """
    Generic class to create a DAG with nodes and edges, and nodes depths.
    """
    def __init__(self):
        """
        Initialize an adjacency list to represent the graph and a dictionary to store node depths.
        """
        self.adjacency_list = {}
        self.depths = {}
        self.nodes_to_group = {}

    def add_node(self, node: Any, should_be_groupped: bool = True) -> None:
        """
        Add a node to the graph if it doesn't already exist.
        Nodes are stored in a dictionary as key and the value is a list of all the adjacent nodes (here it is set as empty, latter the add_edge function will update its content).
        Args:
            node (Any): node to be added into the graph.
        """
        if node not in self.adjacency_list:
            self.adjacency_list[node] = []
            self.nodes_to_group[node] = should_be_groupped  # Track which nodes should be considered when groupping

    def remove_node(self, node: Any) -> None:
        """
        Remove a node from the graph if it exists.
        Args:
            node (Any): node in graph.
        """
        if node in self.adjacency_list:
            del self.adjacency_list[node]

    def add_edge(self, from_node: Any, to_node: Any) -> None:
        """
        Add a directed edge from from_node to to_node in the graph.
        The edge is stored in a dictionary where the key is the from_node and the value is a list of all the adjacent to_nodes.
        Args:
            from_node (Any): source node in edge.
            to_node (Any):  target node in edge.
        """
        if from_node not in self.adjacency_list:  # This happens if the dependency specified does not exist.
            self.add_node(from_node, False)  # Add node because silver tables might depend on bronze tables, which are executed elsewhere.
        self.adjacency_list[from_node].append(to_node)

    def calculate_depths(self) -> None:
        """
        Calculate the depth of each node based on dependencies using a topological sorting approach.
        This ensures that each node's depth depends on the maximum depth of its dependencies.
        """
        # Initialize all node depths to zero
        for node in self.adjacency_list:
            self.depths[node] = 0

        # Sort nodes to process dependencies in the correct order
        def topological_sort(node, visited: set, stack: list):
            visited.add(node)
            for neighbor in self.adjacency_list[node]:
                if neighbor not in visited:
                    topological_sort(neighbor, visited, stack)
            stack.append(node)

        # Generate a topological order
        visited = set()
        stack = []
        for node in self.adjacency_list:
            if node not in visited:
                topological_sort(node, visited, stack)

        # Calculate depths based on the topological order
        while stack:
            node = stack.pop()
            for neighbor in self.adjacency_list[node]:
                self.depths[neighbor] = max(self.depths[neighbor], self.depths[node] + 1)

    def get_depth_groups(self) -> dict[int, str]:
        """
        Group nodes by their depth levels and return a dictionary of these groups.
        Returns:
            dict[int, str]: A dictionary containing the groups of nodes per each depth.
        """
        depth_groups = {}
        for node, depth in self.depths.items():
            if depth not in depth_groups:  # If the depth level doesn't exist, create it.
                depth_groups[depth] = []
            if node in self.nodes_to_group and self.nodes_to_group[node]:
                depth_groups[depth].append(node)  # Append the node to the corresponding depth level.
        depth_groups = {depth: nodes for depth, nodes in depth_groups.items() if len(nodes) > 0}  # Filter empty depth levels.
        return depth_groups

    def get_depths_groups_truncated(self, max_nodes_per_depth: int) -> dict[int, str]:
        """
        Group nodes by their depth levels, truncate the amount of nodes by a maximum value and return a dictionary of these groups.
        Args:
            max_nodes_per_depth (int): maximum value of nodes per depth.
        Returns:
            dict[int, str]: A dictionary containing the groups of nodes per each depth.
        """
        new_depth_groups = {}
        new_index = 0
        
        for depth, nodes in sorted(self.get_depth_groups().items()):
            # Split the list of nodes into chunks of max_nodes_per_depth
            for i in range(0, len(nodes), max_nodes_per_depth):
                # Create a new key for each chunk
                new_depth_groups[new_index] = nodes[i:i + max_nodes_per_depth]
                new_index += 1

        return new_depth_groups

# COMMAND ----------

# %run ../common/metadata_sql

from gd_obsd_common.sql_framework import MetadataSQL

# COMMAND ----------

md = MetadataSQL(spark)

# COMMAND ----------

query = f"""
SELECT NotebookPath, Dependencies
FROM {control_table}
WHERE Stream = '{stream}'
"""  # It is possible to filter by enabled notebooks here, but can't log it properly. So it is logged in check_dependencies.
df = md.read(query).toPandas()

# Initialize the DAG
dag = DAG()

# Add schema.table as DAG nodes (each notebook is called as a table, and its foldes is the schema)
for notebook in df['NotebookPath']:
    dag.add_node(notebook.strip().replace("/", "."))  

# Add edges based on Notebook dependencies
for idx, row in df.iterrows():
    if row['Dependencies'] is not None and row['Dependencies'].strip() != "":
        for dependency in row['Dependencies'].split(","):
            dag.add_edge(dependency.strip(), row['NotebookPath'].strip().replace("/", "."))

# Calculate the depths of each notebook
dag.calculate_depths()

# Get the notebooks by depth truncated by a maximum value
depth_groups = dag.get_depths_groups_truncated(max_concurrent_notebooks)

# Replace '.' with '/' in each string
depth_groups = {
    key: [item.replace('.', '/') for item in value]
    for key, value in depth_groups.items()
}

# Output execution groups
print(depth_groups)

# COMMAND ----------

dbutils.notebook.exit(list(depth_groups.values()))
