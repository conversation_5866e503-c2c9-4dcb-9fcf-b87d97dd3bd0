{"cells": [{"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "1a67387c-0c29-4433-8f2c-6bb6a1b9e3c1", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["-------------------------------------------------------------------------------------------------------------------------\n", "-- table       : brnz_entity\n", "-- description : this is the table that contains the list of the available entities\n", "-- created     : by <PERSON><PERSON>\n", "-------------------------------------------------------------------------------------------------------------------------\n", "\n", "DROP TABLE IF EXISTS bronze.brnz_entity;\n", "\n", "CREATE TABLE IF NOT EXISTS bronze.brnz_entity (\n", "\n", "  entity_id              int       comment 'Id of the entity',\n", "  name_ar                string    comment 'Name of the Entity, in arabic',\n", "  name                   string    comment 'Name of the Entity, in english',\n", "  parent_entity          string    comment 'Code of the Entity Parent',\n", "  code                   string    comment 'Code of the Entity',\n", "  parent_or_child_entity string    comment 'Indicates whether the entity is Parent or Child',\n", "  dep_adeo_resolution    boolean   comment 'TBD',\n", "  oracle_hrms            boolean   comment 'TBD',\n", "  status                 string    comment 'Current status of the entity',\n", "  entity_type            string    comment 'Type of the Entity (Local, Semi Government, Federal)',\n", "  follow_hr_law          boolean   comment 'TBD',\n", "  follow_hr_scale        boolean   comment 'TBD',\n", "  input_file_name        string    comment 'This field will containt the API endpoint used to get the data',\n", "  data_source            string    comment 'Name of the Source where record is coming from (file, CRM, API, etc)',\n", "  created_at_ts          timestamp comment 'Control field - Date & Time when the row was created',\n", "  modified_at_ts         timestamp comment 'Control field - Date & Time when the row was last modified',\n", "  current_flag           boolean   comment 'Flag to indicate whether the row is the last one or not'\n", "\n", "\n", "/*\n", "  entity_id                int       comment 'Id of the entity',\n", "  entity_code              string    comment 'Code of the Entity',\n", "  entity_name_en           string    comment 'Name of the Entity, in english',\n", "  entity_name_ar           string    comment 'Name of the Entity, in arabic',\n", "  entity_type              string    comment 'Type of the Entity (Local, Semi Government, Federal)',\n", "  entity_parent_code       string    comment 'Code of the Entity Parent',\n", "  parent_flag              boolean   comment 'Flag to indicate whether the entity is Parent or Not',\n", "  dep_adeo_resolution_flag boolean   comment 'TBD',\n", "  oracle_hrms_flag         boolean   comment 'TBD',\n", "  follow_hr_law_flag       boolean   comment 'TBD',\n", "  follow_hr_scale_flag     boolean   comment 'TBD',\n", "  status                   string    comment 'Current status of the entity',\n", "  sector                   string    comment 'Sector to which the entity belongs',\n", "  input_file_name          string    comment 'This field will containt the API endpoint used to get the data',\n", "  data_source              string    comment 'Name of the Source where record is coming from (file, CRM, API, etc)',\n", "  created_at_ts            timestamp comment 'Control field - Date & Time when the row was created',\n", "  modified_at_ts           timestamp comment 'Control field - Date & Time when the row was last modified',\n", "  current_flag             boolean   comment 'Flag to indicate whether the row is the last one or not'\n", "  */\n", "\n", ")USING DELTA\n", "\n", "COMMENT 'this is the table that contains the list of the available entities';\n", "ALTER TABLE bronze.brnz_entity SET TAGS ('workstream' = 'transversal');\n", "\n", "-------------------------------------------------------------------------------------------------------------------------\n", "-------------------------------------------------------------------------------------------------------------------------\n"]}], "metadata": {"application/vnd.databricks.v1+notebook": {"computePreferences": null, "dashboards": [], "environmentMetadata": {"base_environment": "", "environment_version": "2"}, "inputWidgetPreferences": null, "language": "sql", "notebookMetadata": {"pythonIndentUnit": 4}, "notebookName": "create_bronze_entity", "widgets": {}}, "language_info": {"name": "sql"}}, "nbformat": 4, "nbformat_minor": 0}