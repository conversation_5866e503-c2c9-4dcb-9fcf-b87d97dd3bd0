variables:
  - name: applicationCode
    value: "STREAM"

  # By default, use DEV config for all branches except UAT and PROD environments
  - ${{ if or(eq(variables['Build.SourceBranchName'], 'uat'), eq(variables['Build.SourceBranchName'], 'prod')) }}:
  # - group: ${{ variables.applicationCode }}-APP-${{ upper(variables['Build.SourceBranchName']) }}
    - group: ${{ variables.applicationCode }}-INFRA-${{ upper(variables['Build.SourceBranchName']) }}
  - ${{ else }}:
  # - group: ${{ variables.applicationCode }}-APP-DEV
    - group: ${{ variables.applicationCode }}-INFRA-DEV

pool:
  name: $(AGENTPOOL)

stages:
  - stage: CD
    condition: |
      or(
        eq(variables['Build.SourceBranchName'], 'dev'),
        eq(variables['Build.SourceBranchName'], 'uat'),
        eq(variables['Build.SourceBranchName'], 'prod')
      )
    displayName: 'Deploy to Databricks Environment'
    jobs:
      - job: DeployJob
        displayName: 'Deploy to the appropriate environment'
        steps:
          - task: PowerShell@2
            inputs:
              targetType: 'inline'
              script: |
                $CONFIG_PATH = "$env:USERPROFILE/.databrickscfg"

                # Access environment variables using $env: prefix
                $host_url = $env:DATABRICKS_WORKSPACE_URL
                $client_id = $env:ARM_CLIENT_ID
                $client_secret = $env:ARM_CLIENT_SECRET
                $tenant_id = $env:ARM_TENANT_ID

                $configContent = @"
                [AZDO]
                host = $host_url
                azure_client_id = $client_id
                azure_client_secret = $client_secret
                azure_tenant_id = $tenant_id
                "@

                Set-Content -Path $CONFIG_PATH -Value $configContent
                Write-Host "##vso[task.setvariable variable=DATABRICKS_CONFIG_FILE]$CONFIG_PATH"
            env:
              ARM_CLIENT_ID: $(TERRAFORM_CLIENT_ID)
              ARM_CLIENT_SECRET: $(TERRAFORM_CLIENT_SECRET)
              ARM_TENANT_ID: $(TENANT_ID)
              DATABRICKS_WORKSPACE_URL: $(DATABRICKS_WORKSPACE_URL)
            displayName: 'Configure Databricks CLI'
          
          - task: PowerShell@2
            inputs:
              targetType: 'inline'
              script: |
                # Define the log file path
                $outputLogPath = "$(Build.ArtifactStagingDirectory)/databricks_repo_update.log"
                
                # Run the databricks repos update command and capture output
                $output = & databricks repos update /Workspace/CICD/$(Build.Repository.Name) --branch $(Build.SourceBranchName) --profile AZDO 2>&1
                
                # Save the output to a log file for artifact preservation
                $output | Out-File -FilePath $outputLogPath
                
                # Display the output
                Write-Output "Databricks repos update command output:"
                Write-Output $output
                
                # Check for errors in the output
                $lastLine = $output | Select-Object -Last 1
                if ($output -match "Error:" -or $LASTEXITCODE -ne 0) {
                    Write-Error "Task failed due to error detected in Databricks repo update output: $output"
                    exit 1
                } else {
                    Write-Output "No error detected in Databricks repo update output."
                }
            displayName: 'Update Databricks Git Folder'


