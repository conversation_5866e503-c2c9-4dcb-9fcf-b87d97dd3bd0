{"dev": {"all": "https://prod-10.uaenorth.logic.azure.com:443/workflows/346d0c0ec8114db191b3d611b4eb6962/triggers/manual/paths/invoke?api-version=2016-06-01&sp=%2Ftriggers%2Fmanual%2Frun&sv=1.0&sig=LLl-8Ij33c-GRhM4U_hfqulDsca3b8CE9N6FNWVG5pM"}, "uat": {"all": "https://prod-24.uaenorth.logic.azure.com:443/workflows/d73d4c54b3dd49559c07f557244887af/triggers/manual/paths/invoke?api-version=2016-06-01&sp=%2Ftriggers%2Fmanual%2Frun&sv=1.0&sig=fDNaousCRH35C3CViE7DW8NupPp_ylKgY1tbVEcIWls"}, "prod": {"corpservices": "https://prod-00.uaenorth.logic.azure.com:443/workflows/e88c946f09174dceb8ddf369f20202f6/triggers/manual/paths/invoke?api-version=2016-06-01&sp=%2Ftriggers%2Fmanual%2Frun&sv=1.0&sig=M4hDjj5D0Wvx6VBU5VNq87agMNlAerYANef0wO2t9RM", "servicenow": "https://prod-20.uaenorth.logic.azure.com:443/workflows/8f58565f25f946378eac43e9b2d6be0c/triggers/manual/paths/invoke?api-version=2016-06-01&sp=%2Ftriggers%2Fmanual%2Frun&sv=1.0&sig=-cYNvsboX-gpBajRjnzYWbAIWopUuBvycgC2f9-cnR8", "saff": "https://prod-10.uaenorth.logic.azure.com:443/workflows/87f79e842f934c5584c5d22e29dec416/triggers/manual/paths/invoke?api-version=2016-06-01&sp=%2Ftriggers%2Fmanual%2Frun&sv=1.0&sig=m5GMDE8NHrxmq1Nula8LvegSEFbhX0n0EnuF7UNerOw", "cybersecurity": "https://prod-09.uaenorth.logic.azure.com:443/workflows/25510ff8263249bc92d2ccdf4c990da7/triggers/manual/paths/invoke?api-version=2016-06-01&sp=%2Ftriggers%2Fmanual%2Frun&sv=1.0&sig=gF-f0Z9nM686f129VslXBOsEmxkNl0mSRLODM2KCld0", "cx": "https://prod-25.uaenorth.logic.azure.com:443/workflows/84bcacd096984c48b101448371a23327/triggers/manual/paths/invoke?api-version=2016-06-01&sp=%2Ftriggers%2Fmanual%2Frun&sv=1.0&sig=c0XGwt7Z-Z6LcymyJJ-0qFIYh7_jkKLKldr6qMziDlo", "tamm": "https://prod-00.uaenorth.logic.azure.com:443/workflows/ba2e05cd7c064621a75b4d64027524a7/triggers/manual/paths/invoke?api-version=2016-06-01&sp=%2Ftriggers%2Fmanual%2Frun&sv=1.0&sig=LW2i7IKMmFyiUslYtpbg7P8pskZnGoiUp-LzhJyJ-TE"}}