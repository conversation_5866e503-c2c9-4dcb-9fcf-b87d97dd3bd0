{"cells": [{"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "a0e4683b-958e-4740-87dc-8f7ac0ba53c5", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["--this insert is temporary.. the table must be filled out by importing the file from the sharepoint\n", "\n", "--select * from bronze.brnz_entity;\n", "\n", "insert into bronze.brnz_entity\n", "values\n", "(131,'جهاز أبوظبي للمحاسبة','Abu Dhabi Accountability Authority','ADAA','ADAA','Parent',FALSE,TRUE,'Active','Local',FALSE,FALSE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(134,'مجلس أبوظبي الرياضي','Abu Dhabi Sports Council','ADDCD','ADSC','Child',TRUE,TRUE,'Active','Local',TRUE,TRUE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(135,'مركز الإحصاء - أبوظبي','Statistics Centre Abu Dhabi','DGE','SCAD','Child',TRUE,TRUE,'Active','Local',TRUE,TRUE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(768,'هيئة الأوقاف وإدارة أموال القصر','Endowments and Minors’ Funds Management Authority','ADAWQAF','ADAWQAF','Parent',TRUE,TRUE,'Active','Local',TRUE,TRUE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(809,'مؤسسة زايد العليا لأصحاب الهمم','Zayed Higher Organization for People of Determination','ZHO','ZHO','Parent',TRUE,TRUE,'Active','Local',TRUE,TRUE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(886,'مجلس أبوظبي للجودة والمطابقة','Abu Dhabi Quality & Conformity Council','QCC','QCC','Parent',TRUE,TRUE,'Active','Local',TRUE,TRUE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(1107,'هيئة البيئة - أبوظبي','Environment Agency-Abu Dhabi','EAD','EAD','Parent',TRUE,TRUE,'Active','Local',TRUE,TRUE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(1487,'بلدية منطقة الظفرة','Al Dhafra Region Municipality','DMT','DRM','Child',TRUE,TRUE,'Active','Local',TRUE,TRUE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(1627,'مؤسسة التنمية الأسرية','Family Development Foundation','ADDCD','FDF','Child',FALSE,TRUE,'Active','Local',TRUE,TRUE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(2447,'مر<PERSON><PERSON> المتابعة والتحكم','Monitor and Control Center','MCC','MCC','Parent',FALSE,TRUE,'Active','Federal',FALSE,FALSE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(2628,'هيئة أبوظبي للدفاع المدني','Abu Dhabi Civil Defense Authority','ADPolice','ADCDA','Child',TRUE,TRUE,'Active','Local',FALSE,FALSE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(2708,'المركز الوطني للتأهيل','Abu Dhabi Rehabilitation Centre','NRC','NRC','Parent',FALSE,TRUE,'Active','Local',FALSE,FALSE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(2732,'ديوان ممثل الحاكم في منطقة العين','Ruler’s Representative Court in Al Ain Region','RRER','RRER','Parent',FALSE,TRUE,'Active','Local',TRUE,TRUE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(2733,'ديوان ممثل الحاكم في منطقة الظفرة','Ruler’s Representative Court in Al Dhafrah Region','RRCDR','RRCDR','Parent',FALSE,TRUE,'Active','Local',TRUE,TRUE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(3511,'دار زايد للثقافة الإسلامية','Zayed House for Islamic Culture','ZHIC','ZHIC','Child',FALSE,TRUE,'Inactive','Local',FALSE,FALSE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(3903,'دائرة الثقافة والسياحة','Department of Culture and Tourism','DCT','DCT','Parent',TRUE,TRUE,'Active','Local',TRUE,TRUE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(4429,'هيئة ابوظبي للاسكان','Abu Dhabi Housing Authority','ADDCD','ADHA','Child',TRUE,TRUE,'Active','Local',TRUE,TRUE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(4670,'دائرة التنمية الاقتصادية - أبوظبي ','Abu Dhabi Department Of Economic Development','ADDED','ADDED','Parent',TRUE,TRUE,'Active','Local',TRUE,TRUE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(4989,'بلدية مدينة العين','Al Ain City Municipality','DMT','AAM','Child',TRUE,TRUE,'Active','Local',TRUE,TRUE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(4993,'دائرة الصحة','Department of Health','DOH','DOH','Parent',TRUE,TRUE,'Active','Local',TRUE,TRUE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(5393,'بلدية مدينة أبوظبي','Abu Dhabi City Municipality','DMT','ADM','Child',TRUE,TRUE,'Active','Local',TRUE,TRUE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(7009,'الأرشيف والمكتبة الوطنية','National Library and Archives','NLA','NLA','Parent',FALSE,TRUE,'Active','Federal',FALSE,FALSE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(7833,'المؤسسة العامة لحديقة الحيوان والأحياء المائية بالعين','The Zoo & Aquarium Public Institution in Al Ain','DCT','ZAPIA','Child',TRUE,TRUE,'Active','Local',TRUE,TRUE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(8405,'مؤسسة الإمارات ','Emirates Foundation','EF','EF','Parent',FALSE,TRUE,'Active','Local',TRUE,TRUE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(8444,'مركز النقل المتكامل','Integrated Transport Centre','DMT','ITC','Child',TRUE,TRUE,'Active','Local',TRUE,TRUE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(9344,'طيران الرئاسة','Presidential Flight','PF','PF','Parent',FALSE,TRUE,'Active','Local',FALSE,FALSE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(10529,'هيئة أبوظبي للزراعة والسلامة الغذائية','Abu Dhabi Agriculture and Food Safety Authority','ADAFSA','ADAFSA','Parent',TRUE,TRUE,'Active','Local',TRUE,TRUE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(10549,'جامعة خليفة ','Khalifa University','KU','KU','Parent',TRUE,TRUE,'Active','Local',FALSE,FALSE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(10609,'جامعة محمد بن زايد للعلوم الإنسانية ','<PERSON> University for Humanities ','MBZUH','MBZUH','Parent',TRUE,TRUE,'Active','Local',TRUE,FALSE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(11963,'كلية الإمارات للتطوير التربوي','Emirates College for Advanced Education','ECAE','ECAE','Parent',TRUE,TRUE,'Active','Local',TRUE,FALSE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(12169,'المجلس الاستشاري الوطني','National Consultative Council','NCC','NCC','Parent',FALSE,TRUE,'Inactive','Local',FALSE,FALSE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(12346,'دائرة التعليم والمعرفة','Department of Education and Knowledge','ADEK','ADEK','Parent',TRUE,TRUE,'Active','Local',TRUE,TRUE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(13346,'مركز أبوظبي للتعليم والتدريب التقني والمهني','Abu Dhabi Centre for Technical and Vocational Education and Training','ACTVET','ACTVET','Parent',TRUE,TRUE,'Active','Local',TRUE,TRUE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(13607,'شركة أبوظبي للخدمات العامة - مساندة','Abu Dhabi General Services Company - Musanada','Musanada','Musanada','Parent',FALSE,TRUE,'Inactive','Semi-Gov',FALSE,FALSE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(15890,'معهد التكنولوجيا التطبيقية','Institute of Applied Technology','ACTVET','IAT','Child',TRUE,TRUE,'Active','Local',TRUE,FALSE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(15891,'معهد أبوظبي للتعليم والتدريب المهني','Abu Dhabi Vocational Education and Training Institute','ACTVET','ADVETI','Child',TRUE,TRUE,'Active','Local',TRUE,TRUE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(16070,'الإدارة العامة للجمارك','Abu Dhabi Customs','ADDED','ADCUSTOMS','Child',TRUE,TRUE,'Active','Local',TRUE,TRUE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(17770,'دائرة تنمية المجتمع','Department of Community Development','ADDCD','ADDCD','Parent',TRUE,TRUE,'Active','Local',TRUE,TRUE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(19530,'دائرة الطاقة','Department of Energy','DOE','DOE','Parent',TRUE,TRUE,'Active','Local',TRUE,TRUE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(19710,'الصندوق الدولي للحفاظ على الحبارى','International Fund for Houbara Conservation','IFHC','IFHC','Parent',FALSE,TRUE,'Active','Local',TRUE,TRUE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(20610,'هيئة أبوظبي للدعم الاجتماعي','Social Support Authority','ADDCD','SSA','Child',TRUE,TRUE,'Active','Local',TRUE,TRUE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(20630,'هيئة المساهمات المجتمعية - معاً','Authority Of Social contribution - Ma an','ADDCD','Maan','Child',TRUE,TRUE,'Active','Local',TRUE,TRUE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(21570,'مكتب أبوظبي الإعلامي','Abu Dhabi Media Office','ADMO','ADMO','Parent',FALSE,TRUE,'Active','Local',FALSE,FALSE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(24531,'دائرة البلديات والنقل','Department of Municipalities & Transport','DMT','DMT','Parent',TRUE,TRUE,'Active','Local',TRUE,TRUE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(24851,'المركز الوطني للأرصاد الجوية','National Center of Meteorology','NCM','NCM','Parent',FALSE,TRUE,'Active','Federal',FALSE,FALSE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(25171,'مركز إدارة الطوارئ والأزمات والكوارث لإمارة أبوظبي','Abu Dhabi Emergences, Crises and Disasters Management Center','ADPolice','ADCMC','Child',TRUE,TRUE,'Active','Local',TRUE,TRUE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(27191,'مكتب أبوظبي للاستثمار','Abu Dhabi Investment Office','ADDED','ADIO','Child',TRUE,TRUE,'Active','Local',TRUE,TRUE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(27271,'جامعة محمد بن زايد للذكاء الاصطناعي','<PERSON> University of Artificial Intelligence','MBZUAI','MBZUAI','Parent',FALSE,TRUE,'Active','Local',TRUE,FALSE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(29152,'الـدائــرة الخاصــة لرئـيس الـدولــة','The Private Department of The President','DOPA','DOPA','Parent',FALSE,TRUE,'Active','Federal',FALSE,FALSE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(31312,'مجلس تنافسية الكوادر الإماراتية','Emirati Talent Competitiveness Council','ETCC','ETCC','Parent',FALSE,TRUE,'Active','Federal',FALSE,FALSE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(31936,'هيئة أبوظبي للطفولة المبكرة','Abu Dhabi Early Childhood Authority','ADDCD','ECA','Child',TRUE,TRUE,'Active','Local',TRUE,TRUE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(32240,'مركز أبوظبي للصحة العامة ','Abu Dhabi Public Health Centre ','DOH','ADPHC','Child',TRUE,TRUE,'Active','Local',TRUE,TRUE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(32296,'مركز اللغة العربية','Abu Dhabi Arabic Language Centre','DCT','ALC','Child',TRUE,TRUE,'Active','Local',TRUE,TRUE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(32536,'مركز أبوظبي للإيواء والرعاية الإنسانية – إيواء','Abu Dhabi Center for Sheltering & Humanitarian Care','EWAA','EWAA','Child',FALSE,TRUE,'Inactive','Local',TRUE,TRUE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(33497,'مجلس أبحاث التكنولوجيا المتطورة','Advanced Technology Research Council ','ATRC','ATRC','Parent',FALSE,TRUE,'Active','Local',TRUE,TRUE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(34358,'هيئة أبوظبي للتراث','Abu Dhabi Heritage Authority','AHA','AHA','Parent',TRUE,TRUE,'Active','Local',TRUE,TRUE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(35662,'هيئة الرعاية الأسرية','Family Care Authority','ADDCD','FCA','Child',TRUE,TRUE,'Active','Local',TRUE,TRUE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(38282,'دائرة التمكين الحكومي','Department  of Government Enablement','DGE','DGE','Parent',TRUE,TRUE,'Active','Local',TRUE,TRUE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(39706,'هيئة الإعلام الإبداعي','Creative Media Authority','ADMO','CMA','Child',FALSE,FALSE,'Active','Federal',FALSE,FALSE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(40023,'الأكاديمية الوطنية لتنمية الطفولة','National Academy for Childhood Development','ECA','NACD','Child',FALSE,FALSE,'Active','Local',TRUE,TRUE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(40383,'صندوق خليفة لتطوير المشاريع','Khalifa Fund For Enterprise Development','ADDED','KFUND','Child',TRUE,TRUE,'Active','Local',TRUE,TRUE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(41763,'مركز بيت العائلة الإبراهيمية','Abrahamic Family House','AFH','AFH','Child',TRUE,TRUE,'Active','Local',TRUE,TRUE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(999991,'شركة أبوظبى للمطارات','Abu Dhabi Airports','ADQ','ADAP','Parent',FALSE,FALSE,'Active','Semi-Gov',FALSE,FALSE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(999992,'غرفة تجارة وصناعة أبوظبي','Abu Dhabi Chamber','ADCCI','ADCCI','Parent',FALSE,FALSE,'Active','Semi-Gov',FALSE,FALSE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(999993,'مكتب أبوظبي التنفيذي','Abu Dhabi Executive Office ','ADEO','ADEO','Parent',TRUE,FALSE,'Active','Local',TRUE,TRUE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(999994,'صندوق أبوظبي للتنمية','Abu Dhabi Fund for Development','ADFD','ADFD','Parent',FALSE,FALSE,'Inactive','Local',FALSE,FALSE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(999995,'سوق أبوظبي العالمي','Abu Dhabi Global Market','ADGM','ADGM','Parent',FALSE,FALSE,'Inactive','Semi-Gov',FALSE,FALSE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(999996,'دائرة القضاء في أبوظبي','Abu Dhabi Judicial Department','ADJD','ADJD','Parent',FALSE,FALSE,'Active','Local',FALSE,FALSE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(999997,'شبكة أبوظبي للإعلام','Abu Dhabi Media Network','ADMO','ADMN','Child',FALSE,FALSE,'Inactive','Semi-Gov',FALSE,FALSE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(999998,'شركة أبوظبي الوطنية للمعارض','Abu Dhabi National Exhibition Centre','ADNEC','ADNEC','Parent',FALSE,FALSE,'Inactive','Semi-Gov',FALSE,FALSE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(999999,'شركة بترول أبوظبي الوطنية - أدنوك','Abu Dhabi National Oil Company','ADNOC','ADNOC','Parent',FALSE,FALSE,'Inactive','Semi-Gov',FALSE,FALSE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(9999910,'القيادة العامة لشرطة أبوظبي ','Abu Dhabi Police Force','ADPolice','ADPolice','Parent',TRUE,TRUE,'Active','Local',FALSE,FALSE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(9999911,'شركة أبوظبي للموانىء','Abu Dhabi Ports','ADQ','ADPorts','Child',FALSE,FALSE,'Inactive','Semi-Gov',FALSE,FALSE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(9999912,'صندوق أبوظبي للتقاعد ','Abu Dhabi Pension Fund','ADPF','ADPF','Parent',FALSE,FALSE,'Active','Local',FALSE,FALSE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(9999913,'شركة ابوظبي التنموية القابضة','Abu Dhabi-based investment and holding company','ADQ','ADQ','Parent',FALSE,FALSE,'Inactive','Semi-Gov',FALSE,FALSE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(9999914,'شركة أبوظبي لحلول المياه المستدامة','Sustainable Water Solutions Company','ADQ','SWS','Child',FALSE,FALSE,'Inactive','Semi-Gov',FALSE,FALSE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(9999915,'سوق أبوظبي للأوراق المالية','Abu Dhabi Securities Exchange','ADX','ADX','Parent',FALSE,FALSE,'Inactive','Semi-Gov',FALSE,FALSE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(9999917,'متحف العين','Al Ain Museum','DCT','AM','Child',FALSE,FALSE,'Active','Local',TRUE,TRUE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(9999918,'ديوان ولي العهد','The Crown Prince Court','CPC','CPC','Parent',FALSE,FALSE,'Inactive','Local',FALSE,FALSE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(9999919,'مجموعة تدوير','Tadweer Group','CWM','CWM','Parent',FALSE,TRUE,'Inactive','Local',FALSE,FALSE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(9999920,'الشركة الوطنية للضمان الصحي - ضمان','National Health Insurance Company – Daman','ADQ','DAMAN','Child',FALSE,FALSE,'Inactive','Semi-Gov',FALSE,FALSE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(9999921,'دائرة المالية - أبوظبي','Department of Finance - Abu Dhabi','DOF','DOF','Parent',FALSE,TRUE,'Active','Local',TRUE,TRUE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(9999922,'مؤسسة الإمارات للطاقة النووية ','Emirates Nuclear Energy Corporation','ADQ','ENEC','Child',FALSE,FALSE,'Inactive','Semi-Gov',FALSE,FALSE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(9999923,'شركة قصر الامارات','Emirates Palace','EP','EP','Parent',FALSE,FALSE,'Inactive','Semi-Gov',FALSE,FALSE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(9999924,'مجموعة الإتحاد للطيران ش.م.ع','Etihad Airways Group','ADQ','Etihad','Child',FALSE,FALSE,'Inactive','Semi-Gov',FALSE,FALSE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(9999925,'الاتحاد للقطارات','Etihad Rail','Etihad Rail','EtihadRail','Parent',FALSE,FALSE,'Inactive','Semi-Gov',FALSE,FALSE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(9999926,'إدارة رأس المال البشري - مكتب أبوظبي التنفيذي ','human Capital Management','ADEO','HCM','Parent',FALSE,FALSE,'Inactive','Local',FALSE,FALSE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(9999927,'مؤسسة خليفة بن زايد آل نهيان للأعمال الإنسانية','Khalifa Bin Zayed Al Nahyan Foundation','KBZF','KBZF','Parent',FALSE,FALSE,'Active','Local',TRUE,TRUE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(9999928,'المؤسسة العليا للمناطق الإقتصادية المتخصصة ','ZonesCorp','ADQ','KEZAD','Child',FALSE,FALSE,'Inactive','Semi-Gov',FALSE,FALSE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(9999930,'اللوفر أبوظبي','Louvre Abu Dhabi','DCT','Louver','Child',FALSE,FALSE,'Active','Local',TRUE,TRUE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(9999931,'صندو<PERSON> محمد بن زايد للمحافظة على الكائنات الحية','<PERSON> Species Conservation Fund','MBZF','MBZF','Parent',FALSE,FALSE,'Active','Local',TRUE,TRUE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(9999932,'مدن العقارية','Modon Properties','ADQ','Modon','Child',FALSE,FALSE,'Inactive','Semi-Gov',FALSE,FALSE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(9999933,'شركة مبادلة للاستثمار','Mubadala Investment Company','Mubadala','Mubadala','Parent',FALSE,FALSE,'Inactive','Semi-Gov',FALSE,FALSE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(9999934,'مكتب رئيس المجلس التنفيذي','President of Executive Council Office','ADEO','PECO','Child',FALSE,FALSE,'Active','Local',TRUE,TRUE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(9999935,'أكاديمية ربدان','Rabdan Academy','RA','RA','Parent',TRUE,FALSE,'Active','Local',FALSE,FALSE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(9999936,' المجلس الأعلى للشؤون المالية والاقتصادية','Supreme Council for Financial and Economic Affairs','SCFEA','SCFEA','Parent',FALSE,FALSE,'Inactive','Semi-Gov',FALSE,FALSE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(9999937,'شركة أبوظبي للخدمات الصحية - صحة','Abu Dhabi Health Services, Co. - SEHA','SEHA','SEHA','Parent',FALSE,FALSE,'Inactive','Semi-Gov',FALSE,FALSE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(9999938,'الشركة القابضة العامة - صناعات','Senaat - General Holding Corporation ','ADQ','SENAAT','Parent',FALSE,FALSE,'Inactive','Semi-Gov',FALSE,FALSE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(9999939,'الأمانة العامة للمجلس الأعلى للبترول','Supreme Petroleum Council','SPC','SPC','Parent',FALSE,FALSE,'Inactive','Semi-Gov',FALSE,FALSE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(9999940,'جامعة السوربون أبوظبي','Sorbonne University Abu Dhabi','SUAD','SUAD','Parent',TRUE,FALSE,'Active','Local',FALSE,FALSE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(9999941,'شركة أبوظبي الوطنية للطاقة - طاقة','Abu Dhabi National Energy Company','ADQ','TAQA','Child',FALSE,FALSE,'Inactive','Semi-Gov',FALSE,FALSE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(9999942,'شركة التطوير والإستثمار السياحي','Tourism Development & Investment Company','TDIC','TDIC','Parent',FALSE,FALSE,'Inactive','Semi-Gov',FALSE,FALSE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(9999943,'twofour54','twofour54','twofour54','twofour54','Parent',FALSE,FALSE,'Inactive','Semi-Gov',FALSE,FALSE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(9999944,'مؤسسة زايد بن سلطان آل نهيان للأعمال الخيرية والإنسانية','Zayed Charitable & Humanitarian Foundation','ZCHF','ZCHF','Parent',FALSE,FALSE,'Active','Local',TRUE,TRUE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE),\n", "(41143,'مركز أبوظبي لإدارة المواد الخطرة','Abu Dhabi Hazardous Materials Management Centre','ADPolice','ADHMMC','Child',TRUE,TRUE,'Active','Local',FALSE,FALSE,'MDM ADGEs List.xlsx','Sharepoint xlsx file',current_timestamp(),current_timestamp(),TRUE)\n", ";\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n"]}], "metadata": {"application/vnd.databricks.v1+notebook": {"computePreferences": null, "dashboards": [], "environmentMetadata": {"base_environment": "", "environment_version": "2"}, "inputWidgetPreferences": null, "language": "sql", "notebookMetadata": {"pythonIndentUnit": 4}, "notebookName": "brnz_entity", "widgets": {}}, "language_info": {"name": "sql"}}, "nbformat": 4, "nbformat_minor": 0}