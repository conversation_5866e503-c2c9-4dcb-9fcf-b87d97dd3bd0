# Databricks notebook source
stream = dbutils.widgets.get("stream")
group_id = dbutils.widgets.get("group_id")

# COMMAND ----------

!pip install gd_obsd_common

# COMMAND ----------

# %run ./teams_alert
from gd_obsd_common.teams_alert import <PERSON><PERSON>lert, AlertMessage, AlertType, format_duration, get_adf_url

# COMMAND ----------

# %run ../common/metadata_sql
from gd_obsd_common.sql_framework import MetadataSQL


# COMMAND ----------

# Load JSON config with all webhooks
import json
import os
with open('../alerts/teams_webhooks.json', 'r') as f:
    teams_channels_webhooks = json.load(f)

# COMMAND ----------

# Initialize Teams Alert
import os
env = os.environ.get('ENVIRONMENT', 'dev')
channel_webhook = teams_channels_webhooks[env].get(stream)
if not channel_webhook:
    raise ValueError(f"Webhook URL for stream '{stream}' not found in the configuration.")
teams_alert = TeamsAlert(channel_webhook)

# COMMAND ----------

md = MetadataSQL(spark)
df = md.read(
f"""
select 
    RunId,
    Status,
    case 
      when EndTime is null then -1 
      else DATEDIFF(second, StartTime, EndTime) 
    end as Duration
from [Log].[ProcessingFrameworkMasterLog] 
where
groupid = '{group_id}'
and stream = '{stream}'             
"""
)
row = df.first()
master_run_id = row["RunId"]
master_status = row["Status"]
master_duration = row["Duration"]

# COMMAND ----------


rows = md.read(
f"""
SELECT
    SUM(CASE WHEN Status = 'Success' THEN 1 ELSE 0 END) AS SuccessCount,
    SUM(CASE WHEN Status != 'Success' THEN 1 ELSE 0 END) AS NonSuccessCount
FROM [Log].[ProcessingFrameworkChildLog]
WHERE
GroupId = '{group_id}'
and Stream = '{stream}'
"""
).first()

success_count = rows['SuccessCount'] or 0
non_success_count = rows['NonSuccessCount'] or 0

message = f"The processing for the stream `{stream}` has completed successfully." if master_status == 'Success' else f"The processing for the stream `{stream}` contains failures."

alert = AlertMessage(
  f"Processing Framework for {stream.replace('_', ' ').title()}",
  message,
  AlertType.SUCCESS if master_status == 'Success' else AlertType.ERROR
)
metrics = {"Successful Tables": success_count, "Failed Tables": non_success_count, "Duration": format_duration(master_duration), "RunId": master_run_id, "GroupId": group_id}
actions = [{"name": "View ADF", "url": get_adf_url(master_run_id)}]
success = teams_alert.send_alert(alert, metrics=metrics, actions=actions)
