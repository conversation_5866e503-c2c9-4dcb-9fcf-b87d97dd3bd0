{"cells": [{"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "6171229b-d6f6-4add-ab88-c3a598fef0e5", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["-------------------------------------------------------------------------------------------------------------------------\n", "-- table       : dim_date_calendar\n", "-- description : This is the calendar dimension\n", "-- created     : by <PERSON>\n", "-------------------------------------------------------------------------------------------------------------------------\n", "\n", "DROP TABLE IF EXISTS common.dim_date_calendar;\n", "\n", "CREATE TABLE IF NOT EXISTS common.dim_date_calendar (\n", "  datekey INT COMMENT 'Integer in YYYYMMDD format',\n", "  date DATE COMMENT 'Actual date (yyyy-MM-dd)',\n", "  year INT COMMENT 'Year of the date',\n", "  semester INT COMMENT 'Semester (1-2)',\n", "  quarter INT COMMENT 'Quarter number (1-4)',\n", "  month INT COMMENT 'Month number (1-12)',\n", "  day INT COMMENT 'Day of the month (1-31)',\n", "  week INT COMMENT 'Week of the year (1-52/53)',\n", "  semesternamelong STRING COMMENT 'Full semester name (Semester X)',\n", "  semesternameshort STRING COMMENT 'Short semester name (SX)',\n", "  quarternamelong STRING COMMENT 'Full quarter name (Quarter X)',\n", "  quarternameshort STRING COMMENT 'Short quarter name (QX)',\n", "  monthnamelong STRING COMMENT 'Full month name (January)',\n", "  monthnameshort STRING COMMENT 'Short month name (Jan)',\n", "  weeknamelong STRING COMMENT 'Full week name (Week X)',\n", "  weeknameshort STRING COMMENT 'Short week name (WX)',\n", "  dayofweek INT COMMENT 'Day of week (1 = Sunday through 7 = Saturday)',\n", "  yearmonthid INT COMMENT 'Year and month concatenated (YYYYMM)',\n", "  yearmonthstring STRING COMMENT 'Year and month concatenated (YYYY-Jan)',\n", "  yearsemesterstring STRING COMMENT 'Year and semester concatenated (YYYY-SX)',\n", "  yearquarterstring STRING COMMENT 'Year and quarter concatenated (YYYY-QX)',\n", "  yearweekstring STRING COMMENT 'Year and week concatenated (YYYY-WXX)',\n", "  monthyearstring STRING COMMENT 'Month and year concatenated (MMYYYY)',\n", "  monthlongyearstring STRING COMMENT 'Full month name and year concatenated (January 2025)',\n", "  dayofweeknamelong STRING COMMENT 'Full day of week name (Monday)',\n", "  dayofweeknameshort STRING COMMENT 'Short day of week name (Mon)',\n", "  dayofyear INT COMMENT 'Day of the year (1-365/366)'\n", ") USING DELTA\n", "COMMENT 'This is the gold layer for the date table';\n", "\n", "ALTER TABLE common.dim_date_calendar SET TAGS ('workstream' = 'transversal');\n", "\n", "-------------------------------------------------------------------------------------------------------------------------\n", "-------------------------------------------------------------------------------------------------------------------------"]}], "metadata": {"application/vnd.databricks.v1+notebook": {"computePreferences": null, "dashboards": [], "environmentMetadata": {"base_environment": "", "environment_version": "2"}, "inputWidgetPreferences": null, "language": "sql", "notebookMetadata": {"pythonIndentUnit": 4}, "notebookName": "create_dim_date_calendar", "widgets": {}}, "language_info": {"name": "sql"}}, "nbformat": 4, "nbformat_minor": 0}