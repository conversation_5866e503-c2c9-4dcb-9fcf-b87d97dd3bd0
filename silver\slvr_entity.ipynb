{"cells": [{"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "e695e809-36fe-4d26-ba15-3807b6ac54cd", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["with cte_brnz_entity as(\n", "select distinct\n", "       concat_ws('|', cast(entity_id as string), name, code) as integration_id\n", "     , entity_id\n", "     , name_ar                    as entity_name_ar\n", "     , name                       as entity_name_en\n", "     , parent_entity              as entity_parent_code\n", "     , code                       as entity_code     \n", "     , case\n", "            when lcase(parent_or_child_entity) = 'parent' then True\n", "            else False\n", "       end                        as parent_flag\n", "     , dep_adeo_resolution        as dep_adeo_resolution_flag\n", "     , oracle_hrms                as oracle_hrms_flag \n", "     , status\n", "     , entity_type \n", "     , follow_hr_law              as follow_hr_law_flag\n", "     , follow_hr_scale            as follow_hr_scale_flag\n", "  from bronze.brnz_entity\n", ")\n", ",\n", "\n", "cte_slvr_entity_stg as(\n", "  select integration_id\n", "\t     , coalesce(entity_id,                -1)        as entity_id\n", "\t     , coalesce(entity_code,              'Unknown') as entity_code\n", "       , coalesce(entity_name_en,           'Unknown') as entity_name_en\n", "       , coalesce(entity_name_ar,           'Unknown') as entity_name_ar\n", "\t\t\t , coalesce(entity_type,              'Unknown') as entity_type  \n", "       , 'Unknown'                                     as sector  --this field is still not coming in the file\n", "       , coalesce(entity_parent_code,       'Unknown') as entity_parent_code\n", "       , coalesce(parent_flag,              False)     as parent_flag\n", "       , coalesce(dep_adeo_resolution_flag, False)     as dep_adeo_resolution_flag\n", "\t\t\t , coalesce(oracle_hrms_flag,         False)     as oracle_hrms_flag \n", "\t\t   , coalesce(follow_hr_law_flag,       False)     as follow_hr_law_flag\n", "       , coalesce(follow_hr_scale_flag,     False)     as follow_hr_scale_flag\n", "       , coalesce(status,                   'Unknown') as status\n", "    from cte_brnz_entity\n", ")\n", "\n", "merge into silver.slvr_entity target\n", "\tusing (\n", "\t\t\t   select integration_id\n", "\t\t\t\t      , entity_id\n", "\t\t\t        , entity_code\n", "\t\t\t\t      , entity_name_en\n", "\t\t\t\t      , entity_name_ar\n", "\t\t\t\t\t\t\t, entity_type\n", "\t\t\t\t\t\t\t, sector\n", "\t\t\t\t      , entity_parent_code\n", "\t\t\t\t      , parent_flag\n", "\t\t\t\t      , dep_adeo_resolution_flag\n", "\t\t\t\t      , oracle_hrms_flag\n", "\t\t\t\t      , follow_hr_law_flag\n", "\t\t\t\t\t\t\t, follow_hr_scale_flag\n", "\t\t\t\t      , status\n", "           from cte_slvr_entity_stg\n", "\t\t    ) source\n", "       on source.integration_id = target.integration_id\n", "\n", "\twhen matched \n", "\t\t\t\t\t and (\n", "\t\t\t\t\t\t        target.entity_name_ar           <> source.entity_name_ar\n", "\t\t\t\t\t\t\t\t or target.entity_type              <> source.entity_type\t\t\n", "\t\t\t\t\t\t     or target.sector                   <> source.sector\n", "\t\t\t\t\t\t     or target.entity_parent_code       <> source.entity_parent_code\n", "\t\t\t\t\t\t\t\t or target.parent_flag              <> source.parent_flag\n", "\t\t\t\t\t\t\t\t or target.dep_adeo_resolution_flag <> source.dep_adeo_resolution_flag\n", "\t\t\t\t\t\t     or target.oracle_hrms_flag         <> source.oracle_hrms_flag\n", "\t\t\t\t\t\t\t\t or target.follow_hr_law_flag       <> source.follow_hr_law_flag\n", "\t\t\t\t\t\t\t\t or target.follow_hr_scale_flag     <> source.follow_hr_scale_flag\n", "\t\t\t\t\t\t     or target.status                   <> source.status\n", "\t\t\t\t\t\t     \n", "\t\t\t\t\t     )\n", "\t\tthen update \n", "\t\t        set target.entity_name_ar           = source.entity_name_ar\n", "\t\t\t\t\t\t  , target.entity_type         = source.entity_type\n", "\t\t\t\t\t\t\t, target.sector                   = source.sector\n", "\t\t\t\t\t\t  , target.entity_parent_code       = source.entity_parent_code\n", "\t\t\t\t\t\t\t, target.parent_flag              = source.parent_flag\n", "\t\t\t\t\t\t  , target.dep_adeo_resolution_flag = source.dep_adeo_resolution_flag\n", "\t\t\t\t\t\t  , target.oracle_hrms_flag         = source.oracle_hrms_flag\n", "\t\t\t\t\t\t  , target.follow_hr_law_flag       = source.follow_hr_law_flag\n", "\t\t\t\t\t\t\t, target.follow_hr_scale_flag     = source.follow_hr_scale_flag\n", "\t\t\t\t\t\t  , target.status                   = source.status\n", "\t\t\t\t      , target.modified_at_ts           = current_timestamp()\n", "\n", "\twhen not matched then\n", "\t\tinsert (\n", "\t\t\t\t      integration_id\n", "\t\t\t\t    , entity_id\n", "\t\t\t\t    , entity_code\n", "\t\t\t\t    , entity_name_en\n", "\t\t\t\t\t\t, entity_name_ar\n", "\t\t\t\t    , entity_type\n", "\t\t\t\t    , sector\n", "\t\t\t\t    , entity_parent_code\n", "\t\t\t\t    , parent_flag\n", "\t\t\t\t    , dep_adeo_resolution_flag\n", "\t\t\t\t\t\t, oracle_hrms_flag\n", "\t\t\t\t\t\t, follow_hr_law_flag\n", "\t\t\t\t\t\t, follow_hr_scale_flag\n", "\t\t\t\t    , status\n", "\t\t\t\t    , created_at_ts\n", "  \t\t\t\t  , modified_at_ts\n", "\t\t       )\n", "\t\tvalues (\n", "\t\t\t\t      source.integration_id\n", "\t\t\t\t    , source.entity_id\n", "    \t\t\t\t, source.entity_code\n", "\t\t\t\t    , source.entity_name_en\n", "\t\t\t\t\t\t, source.entity_name_ar\n", "\t\t\t\t    , source.entity_type\n", "\t\t\t\t    , source.sector\n", "\t\t\t\t    , source.entity_parent_code\n", "\t\t\t\t    , source.parent_flag\n", "\t\t\t\t    , source.dep_adeo_resolution_flag\n", "\t\t\t\t\t\t, source.oracle_hrms_flag\n", "            , source.follow_hr_law_flag\n", "            , source.follow_hr_scale_flag\n", "\t\t\t\t    , source.status\n", "\t\t\t\t    , current_timestamp() \n", "\t\t\t\t    , current_timestamp() \n", "\t\t       )\n", ";\n", "/*\n", "  /* if the row is not in the Source table, then a soft delete is set*/\n", "\twhen not matched by source\n", "\t and target.current_flag = true then\n", "\t  update \n", "\t\t   set target.current_flag   = false  --soft delete\n", "\t\t     , target.modified_at_ts = current_timestamp()\n", "*/\n", "\n", "\n"]}], "metadata": {"application/vnd.databricks.v1+notebook": {"computePreferences": null, "dashboards": [], "environmentMetadata": {"base_environment": "", "environment_version": "2"}, "inputWidgetPreferences": null, "language": "sql", "notebookMetadata": {"pythonIndentUnit": 4}, "notebookName": "slvr_entity", "widgets": {}}, "language_info": {"name": "sql"}}, "nbformat": 4, "nbformat_minor": 0}