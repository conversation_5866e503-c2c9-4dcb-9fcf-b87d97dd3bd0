{"cells": [{"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "9c9388f2-a668-4fca-a681-21f0a47fb93d", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["-------------------------------------------------------------------------------------------------------------------------\n", "-- table       : gld_dim_entity\n", "-- description : This is the dimension for Measurement that stores the list of entities\n", "-- created     : by <PERSON><PERSON>\n", "-------------------------------------------------------------------------------------------------------------------------\n", "\n", "DROP TABLE IF EXISTS gold.gld_dim_entity;\n", "  \n", "CREATE TABLE IF NOT EXISTS gold.gld_dim_entity (\n", "  entity_key               long      generated always as identity (start with 1 increment by 1) primary key comment 'Dim Surrogate Key',\n", "  integration_id           string    not null comment 'ID based on concatenation of PK fields',\n", "  entity_id                int       comment 'Id of the entity',\n", "  entity_code              string    comment 'Code of the Entity',\n", "  entity_name_en           string    comment 'Name of the Entity, in english',\n", "  entity_name_ar           string    comment 'Name of the Entity, in arabic',\n", "  entity_type              string    comment 'Type of the Entity',\n", "  sector                   string    comment 'Sector to which the entity belongs',\n", "  entity_parent_code       string    comment 'Code of the Entity Parent',\n", "  parent_flag              boolean   comment 'Flag to indicate whether the entity is Parent or Not',\n", "  dep_adeo_resolution_flag boolean   comment 'TBD',\n", "  oracle_hrms_flag         boolean   comment 'TBD',\n", "  follow_hr_law_flag       boolean   comment 'TBD',\n", "  follow_hr_scale_flag     boolean   comment 'TBD',\n", "  status                   string    comment 'Current status of the entity',\n", "  effective_from           date      comment 'Stores the date from which the dimension record is effective (for SCD 2)',\n", "  effective_to             date      comment 'Stores the date up to which the dimension record is effective (for SCD 2)',\n", "  created_at_ts            timestamp comment 'Timestamp the record was created',\n", "  modified_at_ts           timestamp comment 'Timestamp the record was modified',\n", "  current_flag             boolean   comment 'Flag to indicate the current row of the dimension'\n", ")USING DELTA\n", "\n", "COMMENT 'This is the dimension for Measurement that stores the list of entities';\n", "ALTER TABLE gold.gld_dim_entity SET TAGS ('workstream' = 'transversal');\n", "\n", "-------------------------------------------------------------------------------------------------------------------------\n", "-------------------------------------------------------------------------------------------------------------------------\n"]}], "metadata": {"application/vnd.databricks.v1+notebook": {"computePreferences": null, "dashboards": [], "environmentMetadata": {"base_environment": "", "environment_version": "2"}, "inputWidgetPreferences": null, "language": "sql", "notebookMetadata": {"pythonIndentUnit": 4}, "notebookName": "create_gold_entity", "widgets": {}}, "language_info": {"name": "sql"}}, "nbformat": 4, "nbformat_minor": 0}